# 200cm直接光流模式修改报告 v2

## 📋 修改概述

**修改目标：** 
- 200cm以下：直接使用`flow.distance_cm`，完全同步传值，无任何复杂运算
- 200cm以上：恢复原来的光流+气压计加权融合模式
- 所有判断标志都基于光流数据，光流显示200cm才能切换

**核心策略：** 
- 200cm以下：`pos_observation.z = flow.distance_cm` 直接赋值
- 200cm以上：恢复`calculate_flow_altitude_weight()`自适应权重融合
- 完全基于`flow.distance_cm <= 200.0f`进行模式切换

## 🔧 具体修改内容

### 修改1: 速度观测融合逻辑

**文件：** `applications/inav.c`
**位置：** 第133-147行
**函数：** `Get_Pos_Vel_Observation()`

**修改后代码：**
```c
//z - 200cm分段模式：直接光流 vs 加权融合
// 使用光流距离判断高度范围
if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
    // 200cm以下：直接使用光流速度，无任何运算
    raw_vel_observation.z = flow.altitude_velocity;
    alt_source = 3; // 3表示直接光流模式
} else if ((flow.distance_health == 1) && (flow.altitude_weight > 0.01f)) {
    // 200cm以上：恢复光流+气压计加权融合
    raw_vel_observation.z = inav_baro_vel * (1.0f - flow.altitude_weight) + flow.altitude_velocity * flow.altitude_weight;
    alt_source = 2; // 2表示光流+气压计融合
} else {
    // 光流不健康：仅使用气压计
    raw_vel_observation.z = inav_baro_vel;
    alt_source = 1; // 1表示仅气压计
}
```

**关键变化：**
- 200cm以下：直接使用光流速度，无加权运算
- 200cm以上：**恢复原来的加权融合逻辑**
- 三段式判断：直接光流 → 加权融合 → 纯气压计

### 修改2: 位置观测融合逻辑 - **核心修改**

**文件：** `applications/inav.c`
**位置：** 第176-189行
**函数：** `Get_Pos_Vel_Observation()`

**修改后代码：**
```c
// 位置观测融合 - 200cm分段模式：直接光流 vs 加权融合
float baroAltTemp = inav_baro_alt - Baro_Alt_Aemed_Offset;
if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
    // 200cm以下：直接使用光流距离，完全同步传值
    pos_observation.z = flow.distance_cm - Baro_Alt_Aemed_Offset; // 直接赋值，无任何滤波运算
} else if ((flow.distance_health == 1) && (flow.altitude_weight > 0.01f)) {
    // 200cm以上：恢复光流+气压计加权融合
    float flow_alt_temp = flow.distance_cm - Baro_Alt_Aemed_Offset;
    float fused_alt_temp = baroAltTemp * (1.0f - flow.altitude_weight) + flow_alt_temp * flow.altitude_weight;
    pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (fused_alt_temp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
} else {
    // 光流不健康：仅使用气压计位置观测
    pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (baroAltTemp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
}
```

**关键变化：**
- **200cm以下：`pos_observation.z = flow.distance_cm - Baro_Alt_Aemed_Offset`** 
- **直接赋值，无任何滤波、积分、融合运算**
- 200cm以上：完全恢复原来的加权融合算法

### 修改3: 权重计算逻辑恢复

**文件：** `applications/sensors.c`
**位置：** 第198-206行
**函数：** `sensors_flow_altitude_update()`

**修改后代码：**
```c
// 200cm分段权重计算：直接光流 vs 自适应权重
if (flow.distance_health == 1 && flow.distance_cm <= 200.0f) {
    flow.altitude_weight = 1.0f; // 200cm以下：直接光流模式标志
} else if (flow.distance_health == 1) {
    // 200cm以上：恢复基于光流距离的自适应权重计算
    flow.altitude_weight = calculate_flow_altitude_weight(flow.distance_cm);
} else {
    flow.altitude_weight = 0.0f; // 光流不健康：禁用光流测高
}
```

**关键变化：**
- 200cm以下：权重固定为1.0（标志作用）
- **200cm以上：恢复`calculate_flow_altitude_weight(flow.distance_cm)`自适应权重**
- 恢复了原来的分段线性权重算法

### 修改4: 惯导积分逻辑

**文件：** `applications/inav.c`
**位置：** 第531-544行
**函数：** `inav_vel_pos_fast_update()`

**修改后代码：**
```c
if (inav_state.ahrs_alignment_complete == 1)
{
    if (alt_observation_health == 1)
    {
        // 200cm分段模式：直接光流 vs 惯导积分
        if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
            // 200cm以下：直接使用光流观测，不进行惯导积分
            // inav_data.velocity_ef.z 和 inav_data.position_ef.z 直接由观测更新
        } else {
            // 200cm以上：正常使用陀螺仪+加速度计惯导积分
            inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef.z) * dt;
            inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef.z) * dt;
        }
    }
```

**关键变化：**
- 200cm以下：完全不进行惯导积分
- 200cm以上：恢复正常的陀螺仪+加速度计积分

### 修改5: 修正逻辑简化

**文件：** `applications/inav.c`
**位置：** 第369-386行
**函数：** `Pos_Vel_Upate()`

**修改后代码：**
```c
if (alt_observation_health == 1)
{
    // 200cm分段模式：直接光流 vs 正常修正
    if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
        // 200cm以下：直接光流模式，最小化修正运算
        velocity_correction_ef_lpf.z = 0.0f; // 不使用速度修正
        accel_correction_ef_lpf.z = 0.0f;    // 不使用加速度修正
    } else {
        // 200cm以上：恢复正常的加速度计修正
        accel_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Z2], accel_correction_ef.z, dt, 0.2f);
        velocity_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Z], velocity_correction_ef.z, dt, 0.2f);
    }
}
```

**关键变化：**
- 200cm以下：所有修正项设为0，最小化运算
- 200cm以上：恢复正常的滤波修正逻辑

## 📊 系统行为对比

### 200cm以下 - 直接光流模式

**数据流：**
```
flow.distance_cm → pos_observation.z (直接赋值)
flow.altitude_velocity → raw_vel_observation.z (直接赋值)
↓
inav_data.position_ef.z (最终height值)
```

**特点：**
- ✅ **完全同步**：height值直接跟随光流距离
- ✅ **无延迟**：没有任何滤波、积分、融合运算
- ✅ **无波动**：消除所有其他传感器干扰
- ✅ **纯净信号**：直接反映光流传感器读数

### 200cm以上 - 加权融合模式

**数据流：**
```
气压计 + 光流 → 自适应权重融合 → 惯导积分修正 → 最终height值
```

**特点：**
- ✅ **平滑过渡**：恢复原来的分段线性权重
- ✅ **多传感器融合**：充分利用各传感器优势
- ✅ **惯导平滑**：保持陀螺仪+加速度计的平滑特性

## 🎯 预期效果

### 解决的问题

1. **消除200cm以下的波动** - 直接使用`flow.distance_cm`，无任何运算
2. **消除"冲高后回归"** - 200cm以下不使用惯导积分
3. **保持200cm以上的融合优势** - 恢复原来的加权融合算法
4. **基于光流判断切换** - 所有标志都由光流数据决定

### 切换行为

**从200cm以上降到200cm以下：**
```
加权融合模式 → 直接光流模式
(平滑过渡)    (立即同步)
```

**从200cm以下升到200cm以上：**
```
直接光流模式 → 加权融合模式  
(立即同步)    (逐渐融合)
```

## ⚠️ 注意事项

1. **200cm切换点** - 可能有轻微跳变，但应该比之前的波动小得多
2. **光流依赖性** - 200cm以下完全依赖光流可靠性
3. **测试重点** - 重点测试200cm附近的切换行为

## 📝 总结

这次修改实现了您要求的：
- **200cm以下直接传值** - `pos_observation.z = flow.distance_cm`
- **200cm以上恢复融合** - 完全恢复原来的加权融合算法
- **基于光流判断** - 所有切换都基于`flow.distance_cm <= 200.0f`

这应该能彻底解决200cm以下的波动问题，同时保持200cm以上的融合优势。
