#include "data_exchange.h"
#include "parameters.h"
#include "board.h"
#include "pos_vel_att_control.h"
#include "math_utils.h"
#include "inav.h"
#include "rc.h"
#include "controller.h"
#include "drv_usart.h"
#include "drv_optflow.h"
#include "sensors.h"
#include "usbd_cdc_hid_msc.h"

Data_Exchange_Flag data_exchange_flag;
uint8_t data_to_send[50] = {0};

#define data_send(x,y) usbd_cdc_acm_write(x,y)

void Usart3_Data_Receive_Prepare(uint8_t data);
    
void usart1_data_analyse(void)
{
    uint16_t len = 0;//serial_available(SERIAL_PORT1);
    while (len--)
    {
        uint8_t ch = 0;//serial_read_char(SERIAL_PORT1);

        Optflow_ParseChar(ch);
    }
}

void usart3_data_analyse(void)
{
    uint16_t len = 0;//serial_available(SERIAL_PORT3);
    while (len--)
    {
        uint8_t ch = 0;//serial_read_char(SERIAL_PORT3);

        Usart3_Data_Receive_Prepare(ch);
    }
}

void DT_Send_Data(u8 *dataToSend , u8 length)
{
    //nrf24l01_send(dataToSend,length);
}

/////////////////////////////////////////////////////////////////////////////////////
//Data_Receive_Anl������Э�����ݽ������������������Ƿ���Э���ʽ��һ������֡���ú��������ȶ�Э�����ݽ���У��
//У��ͨ��������ݽ��н�����ʵ����Ӧ����
//�˺������Բ����û����е��ã��ɺ���Data_Receive_Prepare�Զ�����
void DT_Data_Receive_Anl(u8 *data_buf,u8 num)
{

    u8 sum = 0,i=0;
    for(i=0;i<(num-1);i++)
        sum += *(data_buf+i);
    if(!(sum==*(data_buf+num-1)))       return;     //�ж�sum
    if(!(*(data_buf)==0xAA && *(data_buf+1)==0xAF))     return;     //�ж�֡ͷ

    if(*(data_buf+2)==0X01)     //��λ�������İ�������У׼
    {
        if(*(data_buf+4)==0X01)   
            ;//mpu6050.Acc_CALIBRATE = 1;
        if(*(data_buf+4)==0X02)
            ;//mpu6050.Gyro_CALIBRATE = 1;
        if(*(data_buf+4)==0X03)
        {
            ;//mpu6050.Acc_CALIBRATE = 1;
            ;//mpu6050.Gyro_CALIBRATE = 1;
        }
    }
 
    else if(*(data_buf+2)==0X02)    //��ȡ�ɻ���Ϣ
    {

			if(*(data_buf+4)==0X01)
        {
//					ANTO_Recived_flag.CMD2_READ_PID = 1;   //��ȡPID ��Ϣ
//					f.send_pid = 1;
//					cnt = 0;
        }
        if(*(data_buf+4)==0X02)
        {

        }
        if(*(data_buf+4)==0XA0)     //��ȡ�汾��Ϣ
        {
            //f.send_version = 1;
        }
        if(*(data_buf+4)==0XA1)     //�ָ�Ĭ�ϲ���
        {
 //           Sort_PID_Flag=2;
		    //pid_param_Init();
        }
    }
    else if(*(data_buf+2)==0X10)                             //PID1
    {								
//			  memcpy(RatePID,&data_buf[4],18);          //�Ȱѽ��յ����������������ֹ����һ��PID���ݸ��ǣ������PID�Ǹ��ٶȻ��õ�
//				ANTO_Recived_flag.PID1 = 1;              //���յ���λ��������PID���� �任��־			
//        ANO_DT_Send_Check(*(data_buf+2),sum);    //����У����				
    }
    else if(*(data_buf+2)==0X11)                             //PID2
    {
//				memcpy(AnglePID,&data_buf[4],18);				 //�Ȱѽ��յ����������������ֹ����һ��PID���ݸ��ǣ������PID�Ǹ��ٶȻ��õ�	
//			  ANTO_Recived_flag.PID2 = 1;						  //���յ���λ��������PID����  �任��־
//        ANO_DT_Send_Check(*(data_buf+2),sum);   //����У����
    }
    else if(*(data_buf+2)==0X12)                             //PID3
    {    
//				memcpy(HighPID,&data_buf[4],18);				 //�Ȱѽ��յ����������������ֹ����һ��PID���ݸ��ǣ������PID�Ǹ��ٶȻ��õ�	
//				ANTO_Recived_flag.PID3 = 1;						  //���յ���λ��������PID���� �任��־
//        ANO_DT_Send_Check(*(data_buf+2),sum);   //����У����				       
    }
    else if(*(data_buf+2)==0X13)                             //PID4
    {
    
//			memcpy(HighPID4,&data_buf[4],18);				 //�Ȱѽ��յ����������������ֹ����һ��PID���ݸ��ǣ������PID�Ǹ��ٶȻ��õ�	
//			ANTO_Recived_flag.PID4 = 1;						  //���յ���λ��������PID���� �任��־
//			ANO_DT_Send_Check(*(data_buf+2),sum);

    }
    else if(*(data_buf+2)==0X14)                             //PID5
    {
        //ANO_DT_Send_Check(*(data_buf+2),sum);
    }
    else if(*(data_buf+2)==0X15)                             //PID6
    {
        //ANO_DT_Send_Check(*(data_buf+2),sum);
//        Sort_PID_Cnt++;
//        Sort_PID_Flag=1;
    }


}

uint8_t Usart3_Rx_Buffer[64];
void Usart3_Data_Receive_Prepare(uint8_t data)
{
    static uint8_t _data_len = 0, _data_cnt = 0, RxState = 0;
    if (RxState == 0 && data == 0xAA)
    {
        RxState = 1;
        Usart3_Rx_Buffer[0] = data;
    }
    else if (RxState == 1 && data == 0xAF)
    {
        RxState = 2;
        Usart3_Rx_Buffer[1] = data;
    }
    else if (RxState == 2 && data > 0 && data < 0XF1)
    {
        RxState = 3;
        Usart3_Rx_Buffer[2] = data;
    }
    else if (RxState == 3 && data < 50)
    {
        RxState = 4;
        Usart3_Rx_Buffer[3] = data;
        _data_len = data;
        _data_cnt = 0;
    }
    else if (RxState == 4 && _data_len > 0)
    {
        _data_len--;
        Usart3_Rx_Buffer[4 + _data_cnt++] = data;
        if (_data_len == 0)
            RxState = 5;
    }
    else if (RxState == 5)
    {
        RxState = 0;
        Usart3_Rx_Buffer[4 + _data_cnt] = data;
        Recive_Analyse(Usart3_Rx_Buffer, _data_cnt + 5);

    }
    else
        RxState = 0;
}


void Recive_Analyse(uint8_t *data_buf, uint8_t num)
{
    uint8_t i, sum = 0;

    for (i = 0; i < (num - 1); i++)
        sum += *(data_buf + i);
    if (!(sum == *(data_buf + num - 1)))     return;    //�ж�sum
    if (!(*(data_buf) == 0xAA && *(data_buf + 1) == 0xAF))       return;    //�ж�֡ͷ

/////////////////////////////////////////////////////////////////////////////////////
    if (*(data_buf + 2) == 0X01)
    {
        if (*(data_buf + 4) == 0X01)
            Copter.ACC_CALIBRATE = 1;
        if (*(data_buf + 4) == 0X02)
            Copter.GYRO_CALIBRATE = 1;
        if (*(data_buf + 4) == 0X04)
        {
            Copter.MAG_CALIBRATE = 1;
        }
        if (*(data_buf + 4) == 0X08)  // 气压计零点重新校准
        {
            sensors_baro_recalibrate();
        }
    }

    if (*(data_buf + 2) == 0X02)
    {
        if (*(data_buf + 4) == 0X01)
        {
            data_exchange_flag.PID1_Need_Send = 1;
            data_exchange_flag.PID2_Need_Send = 1;
            data_exchange_flag.PID3_Need_Send = 1;
        }
        if (*(data_buf + 4) == 0X02)
        {

        }
    }

    if (*(data_buf + 2) == 0X10)                        //PID1
    {
//      PID[ANGLE_ROL].Kp = (float)((vs16)(*(data_buf+4)<<8)|*(data_buf+5))*0.01f;
//      PID[ANGLE_ROL].Ki = (float)((vs16)(*(data_buf+6)<<8)|*(data_buf+7))*0.01f;
//      PID[ANGLE_ROL].Kd = (float)((vs16)(*(data_buf+8)<<8)|*(data_buf+9))*0.001f ;
//      PID[ANGLE_PIT].Kp = (float)((vs16)(*(data_buf+10)<<8)|*(data_buf+11))*0.01f;
//      PID[ANGLE_PIT].Ki = (float)((vs16)(*(data_buf+12)<<8)|*(data_buf+13))*0.01f ;
//      PID[ANGLE_PIT].Kd = (float)((vs16)(*(data_buf+14)<<8)|*(data_buf+15))*0.001f;
//      PID[ANGLE_YAW].Kp = (float)((vs16)(*(data_buf+16)<<8)|*(data_buf+17))*0.01f;
//      PID[ANGLE_YAW].Ki = (float)((vs16)(*(data_buf+18)<<8)|*(data_buf+19))*0.01f;
//      PID[ANGLE_YAW].Kd = (float)((vs16)(*(data_buf+20)<<8)|*(data_buf+21))*0.001f;
//
//
//      Send_Check(*(data_buf+2),sum);
//      Save_Parameter_Data();

    }
    if (*(data_buf + 2) == 0X11)                        //PID2
    {
//      PID[RATE_ROL].Kp = (float)((vs16)(*(data_buf+4)<<8)|*(data_buf+5)) * 0.01;
//      PID[RATE_ROL].Ki = (float)((vs16)(*(data_buf+6)<<8)|*(data_buf+7)) * 0.01;
//      PID[RATE_ROL].Kd = (float)((vs16)(*(data_buf+8)<<8)|*(data_buf+9)) * 0.001;
//      PID[RATE_PIT].Kp = (float)((vs16)(*(data_buf+10)<<8)|*(data_buf+11)) * 0.01;
//      PID[RATE_PIT].Ki = (float)((vs16)(*(data_buf+12)<<8)|*(data_buf+13)) * 0.01;
//      PID[RATE_PIT].Kd = (float)((vs16)(*(data_buf+14)<<8)|*(data_buf+15)) * 0.001;
//      PID[RATE_YAW].Kp = (float)((vs16)(*(data_buf+16)<<8)|*(data_buf+17)) * 0.01;
//      PID[RATE_YAW].Ki = (float)((vs16)(*(data_buf+18)<<8)|*(data_buf+19)) * 0.01;
//      PID[RATE_YAW].Kd = (float)((vs16)(*(data_buf+20)<<8)|*(data_buf+21)) * 0.001;
//      Send_Check(*(data_buf+2),sum);
//      Save_Parameter_Data();
    }
    if (*(data_buf + 2) == 0X12)                        //PID3
    {
        //PID[Alt_Hold].Kp = (float)((vs16)(*(data_buf+4)<<8)|*(data_buf+5)) * 0.01;
        //PID[Alt_Hold].Ki = (float)((vs16)(*(data_buf+6)<<8)|*(data_buf+7)) * 0.01;
        //PID[Alt_Hold].Kd = (float)((vs16)(*(data_buf+8)<<8)|*(data_buf+9)) * 0.001;
        //PID[X_Hold].Kp = (float)((vs16)(*(data_buf+10)<<8)|*(data_buf+11)) * 0.01;
        //PID[X_Hold].Ki = (float)((vs16)(*(data_buf+12)<<8)|*(data_buf+13)) * 0.01;
        //PID[X_Hold].Kd = (float)((vs16)(*(data_buf+14)<<8)|*(data_buf+15)) * 0.001;
        //PID[Y_Hold].Kp = (float)((vs16)(*(data_buf+16)<<8)|*(data_buf+17)) * 0.01;
        ///PID[Y_Hold].Ki = (float)((vs16)(*(data_buf+18)<<8)|*(data_buf+19)) * 0.01;
        //PID[Y_Hold].Kd = (float)((vs16)(*(data_buf+20)<<8)|*(data_buf+21)) * 0.001;
//      Send_Check(*(data_buf+2),sum);
//      Save_Parameter_Data();
    }
    if (*(data_buf + 2) == 0X13)                        //PID4
    {

        //PID[Speed_Hold].Kp = (float)((vs16)(*(data_buf+4)<<8)|*(data_buf+5)) * 0.01;
        //PID[Speed_Hold].Ki = (float)((vs16)(*(data_buf+6)<<8)|*(data_buf+7)) * 0.01;
        //PID[Speed_Hold].Kd = (float)((vs16)(*(data_buf+8)<<8)|*(data_buf+9)) * 0.001;
        //PID[RATE_PIT].Kp = (float)((vs16)(*(data_buf+10)<<8)|*(data_buf+11)) * 0.01;
        //PID[RATE_PIT].Ki = (float)((vs16)(*(data_buf+12)<<8)|*(data_buf+13)) * 0.01;
        //PID[RATE_PIT].Kd = (float)((vs16)(*(data_buf+14)<<8)|*(data_buf+15)) * 0.001;
        //PID[RATE_YAW].Kp = (float)((vs16)(*(data_buf+16)<<8)|*(data_buf+17)) * 0.01;
        //PID[RATE_YAW].Ki = (float)((vs16)(*(data_buf+18)<<8)|*(data_buf+19)) * 0.01;
        //PID[RATE_YAW].Kd = (float)((vs16)(*(data_buf+20)<<8)|*(data_buf+21)) * 0.001;
//      Send_Check(*(data_buf+2),sum);
//      Save_Parameter_Data();

    }
}


uint8_t Data_Exchange_CNT = 0;

void Data_Exchange(void)
{

    switch (Data_Exchange_CNT)
    {

    case 1:
        Send_Status();
        break;

    case 2:
        Send_Sensor();
        break;

    case 3:
        Send_RCData();
        break;

    case 4:
        Send_MotoPWM();
        break;

    case 5:
    {
        Send_GPS();
        Data_Exchange_CNT = 0;
    }
    break;
    }

    Data_Exchange_CNT++;

}

void Send_Status(void)
{
    uint8_t  i, _cnt = 0, sum = 0;
    vs16 _temp  = 0;
    vs32 _temp2 = inav_data.position_ef.z;
    vs8  _temp1  = 0;

    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0x01;
    data_to_send[_cnt++] = 17; // 原12字节 + 5字节光流测高数据

				rt_kprintf("height = %d\r\n", (rt_int16_t)inav_data.position_ef.z);
	
    //��̬
    _temp = (int)(inav_data.euler_angle.roll * 100);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    _temp = (int)(-inav_data.euler_angle.pitch * 100);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    _temp = (int)(inav_data.euler_angle.yaw * 100);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    //�߶�
    data_to_send[_cnt++] = BYTE3(_temp2);
    data_to_send[_cnt++] = BYTE2(_temp2);
    data_to_send[_cnt++] = BYTE1(_temp2);
    data_to_send[_cnt++] = BYTE0(_temp2);
    //����ģʽ
		_temp1 = Copter.Flight_Mode;
    data_to_send[_cnt++] = BYTE0(_temp1);
    //����״̬
    _temp1 = Copter.ARMED;
    data_to_send[_cnt++] = BYTE0(_temp1);

    // 光流测高数据 - 为上位机图形化显示提供关键参数
    extern uint8_t alt_source; // 高度观测源标记
    _temp1 = alt_source;
    data_to_send[_cnt++] = BYTE0(_temp1);

    _temp1 = flow.distance_health; // 光流距离健康状态
    data_to_send[_cnt++] = BYTE0(_temp1);

    _temp1 = flow.altitude_fusion_active; // 光流测高融合激活标志
    data_to_send[_cnt++] = BYTE0(_temp1);

    _temp = (int)(flow.altitude_weight * 100); // 光流测高权重(0.0-1.0转换为0-100)
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    for (i = 0; i < _cnt; i++)
        sum += data_to_send[i];
    data_to_send[_cnt++] = sum;

    data_send(data_to_send, _cnt);
}



void Send_Sensor(void)
{
    uint8_t i, sum = 0,  _cnt = 0;
    int16_t _temp;

    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0x02;
    data_to_send[_cnt++] = 18;


    _temp = (int16_t)sensors.accel.x;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    _temp = (int16_t)sensors.accel.y;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    _temp = (int16_t)sensors.accel.z;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);


    _temp = (int16_t)sensors.gyro.x;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    _temp = (int16_t)sensors.gyro.y;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    _temp = (int16_t)sensors.gyro.z;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);


    _temp = (int16_t)sensors.mag.x;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    _temp = (int16_t)sensors.mag.y;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    _temp = (int16_t)sensors.mag.z;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    for (i = 0; i < _cnt; i++)
        sum += data_to_send[i];

    data_to_send[_cnt++] = sum;

    data_send(data_to_send, _cnt);
}


void Send_RCData(void)
{
    uint8_t i, sum = 0, _cnt = 0;
    uint16_t _temp = 0;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0x03;
    data_to_send[_cnt++] = 20;
    data_to_send[_cnt++] = BYTE1(Rc.Data[THROTTLE]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[THROTTLE]);

    data_to_send[_cnt++] = BYTE1(Rc.Data[YAW]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[YAW]);
    data_to_send[_cnt++] = BYTE1(Rc.Data[ROLL]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[ROLL]);
    data_to_send[_cnt++] = BYTE1(Rc.Data[PITCH]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[PITCH]);
    data_to_send[_cnt++] = BYTE1(Rc.Data[AUX1]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[AUX1]);
    data_to_send[_cnt++] = BYTE1(Rc.Data[AUX2]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[AUX2]);
    data_to_send[_cnt++] = BYTE1(Rc.Data[AUX3]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[AUX3]);
    data_to_send[_cnt++] = BYTE1(Rc.Data[AUX4]);
    data_to_send[_cnt++] = BYTE0(Rc.Data[AUX4]);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    for (i = 0; i < _cnt; i++)
        sum += data_to_send[i];

    data_to_send[_cnt++] = sum;

    data_send(data_to_send, _cnt);
}

extern float motor_out[MAXMOTORS];
void Send_MotoPWM(void)
{
    uint8_t i, sum = 0, _cnt = 0;
    uint16_t _temp = 0, Moto_PWM[4];

    for (i = 0; i < 4; i++)
        Moto_PWM[i] = motor_out[i];

    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0x06;
    data_to_send[_cnt++] = 16;
    data_to_send[_cnt++] = BYTE1(Moto_PWM[0]);
    data_to_send[_cnt++] = BYTE0(Moto_PWM[0]);
    data_to_send[_cnt++] = BYTE1(Moto_PWM[1]);
    data_to_send[_cnt++] = BYTE0(Moto_PWM[1]);
    data_to_send[_cnt++] = BYTE1(Moto_PWM[2]);
    data_to_send[_cnt++] = BYTE0(Moto_PWM[2]);
    data_to_send[_cnt++] = BYTE1(Moto_PWM[3]);
    data_to_send[_cnt++] = BYTE0(Moto_PWM[3]);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp);

    for (i = 0; i < _cnt; i++)
        sum += data_to_send[i];

    data_to_send[_cnt++] = sum;

    data_send(data_to_send, _cnt);
}



void Send_GPS(void)
{
    uint8_t i, sum = 0, _cnt = 0;
    u32 GPS;
    int16_t _temp = 0;

    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0x04;
    data_to_send[_cnt++] = 16;

    data_to_send[_cnt++] = 0;
    data_to_send[_cnt++] = inav_gps_numsv; //��������

    GPS = inav_gps_longitude; //;
    data_to_send[_cnt++] = BYTE3(GPS);
    data_to_send[_cnt++] = BYTE2(GPS);
    data_to_send[_cnt++] = BYTE1(GPS);
    data_to_send[_cnt++] = BYTE0(GPS); //γ��

    GPS = inav_gps_latitude;
    data_to_send[_cnt++] = BYTE3(GPS);
    data_to_send[_cnt++] = BYTE2(GPS);
    data_to_send[_cnt++] = BYTE1(GPS);
    data_to_send[_cnt++] = BYTE0(GPS); //ji��

    //gpsx.altitude = -45230;
    //_temp =(uint16_t)gpsx.altitude;
    data_to_send[_cnt++] = BYTE1(GPS);
    data_to_send[_cnt++] = BYTE0(GPS); ///daogu

    _temp = 0;
    data_to_send[_cnt++] = BYTE1(_temp);
    data_to_send[_cnt++] = BYTE0(_temp); //�ٶȷ���

    data_to_send[_cnt++] = 1;
    data_to_send[_cnt++] = 1; //�ٶ�

    for (i = 0; i < _cnt; i++)
        sum += data_to_send[i];

    data_to_send[_cnt++] = sum;

    data_send(data_to_send, _cnt);
}

extern uint32_t time_cnt;
float flow_test = 1;
extern euler_angle_t inav_angle_error_from_gps;
extern float GPS_ACC_E, ex, Target_Alt, Thr;
extern vector3f_t vel_observation, pos_observation, raw_vel_observation, delayed_acc_ef, acc_ef_bias, Acc_ef, velocity_correction_ef;
extern float vx, vy, vz;
extern float rc_cmd_roll_raw, rc_cmd_pitch_raw, rc_cmd_yaw_raw, rc_cmd_thr_raw, rc_cmd_roll_lpf, rc_cmd_pitch_lpf, rc_cmd_yaw_lpf, rc_cmd_thr_lpf;
void Send_USER_DATA(void)
{
    uint8_t i = 0, sum = 0, _cnt = 0;
    float temp;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xAA;
    data_to_send[_cnt++] = 0xF1;
    data_to_send[_cnt++] = 0;

    temp = flow.vel_y_raw; //inav_out.angle_roll_observation;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = flow.vel_y_lpf;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    // 光流测高调试数据
    temp = (float)flow.distance_raw; // 原始距离数据(mm)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = flow.distance_cm; // 滤波后距离(cm)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = flow.altitude_velocity; // 光流垂直速度(cm/s)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = flow.altitude_weight; // 光流测高权重(0.0-1.0)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = (float)flow.altitude_fusion_active; // 融合激活标志(0/1)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    // 光流水平坐标数据
    temp = flow.vel_ef_cm_x; // 光流X轴地球坐标系速度(cm/s)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = flow.vel_ef_cm_y; // 光流Y轴地球坐标系速度(cm/s)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    // 观测源标记和健康状态
    extern uint8_t alt_source; // 高度观测源：1=气压计，2=光流+气压计融合
    temp = (float)alt_source;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = (float)flow.distance_health; // 光流距离健康状态(0/1)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = (float)flow.gyro_compensation; // 陀螺仪补偿系数(调试监控)
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = -inav_data.delayed_rate_dps_x * flow_test;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);


    temp = vel_observation.y;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = inav_data.velocity_ef.x;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);


    temp = inav_data.velocity_ef.y; //inav_out.acc_ef.z;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = sensors.baro.vel_cm; //velocity_ef.z;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);


    temp = inav_data.velocity_ef.z;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = pos_observation.z;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = inav_data.position_ef.z;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);

    temp = (float)time_cnt;
    data_to_send[_cnt++] = BYTE3(temp);
    data_to_send[_cnt++] = BYTE2(temp);
    data_to_send[_cnt++] = BYTE1(temp);
    data_to_send[_cnt++] = BYTE0(temp);


    data_to_send[3] = _cnt - 4;

    for (i = 0; i < _cnt; i++)
        sum += data_to_send[i];
    data_to_send[_cnt++] = sum;

    data_send(data_to_send, _cnt);
}




