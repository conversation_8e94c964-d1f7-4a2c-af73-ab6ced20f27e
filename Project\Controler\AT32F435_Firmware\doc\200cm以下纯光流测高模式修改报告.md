# 200cm以下纯光流测高模式修改报告

## 📋 修改概述

**修改目标：** 将系统改为200cm以下只使用光流测高，完全屏蔽气压计、陀螺仪、加速度计对高度测定的影响。高度判断也完全基于光流距离数据。

**核心策略：** 
- 使用`flow.distance_cm <= 200.0f`作为模式切换条件
- 200cm以下：纯光流测高模式
- 200cm以上：传统气压计+惯导模式

## 🔧 具体修改内容

### 修改1: 速度观测融合逻辑

**文件：** `applications/inav.c`
**位置：** 第133-143行
**函数：** `Get_Pos_Vel_Observation()`

**原始代码：**
```c
//z - 光流测高与气压计融合
// 光流测高健康检查和权重分配
if ((flow.distance_health == 1) && (flow.altitude_weight > 0.01f)) {
    // 光流测高+气压计加权融合
    raw_vel_observation.z = inav_baro_vel * (1.0f - flow.altitude_weight) + flow.altitude_velocity * flow.altitude_weight;
    alt_source = 2; // 2表示光流+气压计融合
} else {
    // 仅使用气压计
    raw_vel_observation.z = inav_baro_vel;
    alt_source = 1; // 1表示仅气压计
}
```

**修改后代码：**
```c
//z - 200cm以下纯光流测高模式
// 使用光流距离判断高度范围
if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
    // 200cm以下：纯光流测高模式
    raw_vel_observation.z = flow.altitude_velocity;
    alt_source = 3; // 3表示纯光流测高
} else {
    // 200cm以上或光流不健康：仅使用气压计
    raw_vel_observation.z = inav_baro_vel;
    alt_source = 1; // 1表示仅气压计
}
```

**影响：**
- 200cm以下完全使用光流速度观测
- 新增`alt_source = 3`标识纯光流模式
- 移除了权重计算依赖

### 修改2: 位置观测融合逻辑

**文件：** `applications/inav.c`
**位置：** 第172-181行
**函数：** `Get_Pos_Vel_Observation()`

**原始代码：**
```c
// 位置观测融合 - 光流测高与气压计
float baroAltTemp = inav_baro_alt - Baro_Alt_Aemed_Offset;
if ((flow.distance_health == 1) && (flow.altitude_weight > 0.01f)) {
    // 光流测高+气压计位置加权融合
    float flow_alt_temp = flow.distance_cm - Baro_Alt_Aemed_Offset;
    float fused_alt_temp = baroAltTemp * (1.0f - flow.altitude_weight) + flow_alt_temp * flow.altitude_weight;
    pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (fused_alt_temp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
} else {
    // 仅使用气压计位置观测
    pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (baroAltTemp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
}
```

**修改后代码：**
```c
// 位置观测融合 - 200cm以下纯光流测高模式
float baroAltTemp = inav_baro_alt - Baro_Alt_Aemed_Offset;
if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
    // 200cm以下：纯光流测高位置观测
    float flow_alt_temp = flow.distance_cm - Baro_Alt_Aemed_Offset;
    pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (flow_alt_temp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
} else {
    // 200cm以上或光流不健康：仅使用气压计位置观测
    pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (baroAltTemp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
}
```

**影响：**
- 200cm以下完全使用光流位置观测
- 移除了加权融合逻辑
- 简化为纯光流或纯气压计模式

### 修改3: 权重计算逻辑

**文件：** `applications/sensors.c`
**位置：** 第198-203行
**函数：** `sensors_flow_altitude_update()`

**原始代码：**
```c
// 计算光流测高权重，基于当前气压计高度
if (sensors.baro.alt_cm_lpf > 0) {
    flow.altitude_weight = calculate_flow_altitude_weight(sensors.baro.alt_cm_lpf);
} else {
    flow.altitude_weight = 0.0f; // 高度无效时禁用光流测高
}
```

**修改后代码：**
```c
// 200cm以下纯光流测高模式权重计算
if (flow.distance_health == 1 && flow.distance_cm <= 200.0f) {
    flow.altitude_weight = 1.0f; // 200cm以下：纯光流测高
} else {
    flow.altitude_weight = 0.0f; // 200cm以上或光流不健康：禁用光流测高
}
```

**影响：**
- 移除了对气压计高度的依赖
- 改为基于光流距离的二进制权重（1.0或0.0）
- 简化了权重计算逻辑

### 修改4: 融合激活标志

**文件：** `applications/sensors.c`
**位置：** 第205-206行
**函数：** `sensors_flow_altitude_update()`

**原始代码：**
```c
// 设置光流测高融合激活标志
flow.altitude_fusion_active = (flow.distance_health == 1 && flow.altitude_weight > 0.01f) ? 1 : 0;
```

**修改后代码：**
```c
// 设置光流测高融合激活标志 - 200cm以下纯光流模式
flow.altitude_fusion_active = (flow.distance_health == 1 && flow.distance_cm <= 200.0f) ? 1 : 0;
```

**影响：**
- 移除了对权重阈值的依赖
- 直接基于光流距离和健康状态判断

### 修改5: 屏蔽陀螺仪+加速度计积分（快速更新）

**文件：** `applications/inav.c`
**位置：** 第516-529行
**函数：** `inav_vel_pos_fast_update()`

**原始代码：**
```c
if (inav_state.ahrs_alignment_complete == 1)
{
    if (alt_observation_health == 1)
    {
        inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef.z) * dt;
        inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef.z) * dt;
    }
```

**修改后代码：**
```c
if (inav_state.ahrs_alignment_complete == 1)
{
    if (alt_observation_health == 1)
    {
        // 200cm以下纯光流模式：屏蔽陀螺仪+加速度计对高度的影响
        if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
            // 纯光流模式：不使用加速度计积分，仅依赖光流观测
            // inav_data.velocity_ef.z 和 inav_data.position_ef.z 仅通过观测修正更新
        } else {
            // 200cm以上：正常使用陀螺仪+加速度计积分
            inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef.z) * dt;
            inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef.z) * dt;
        }
    }
```

**影响：**
- **关键修改**：200cm以下完全屏蔽陀螺仪+加速度计的高度积分
- 高度估计仅依赖光流观测数据
- 消除了"冲高后回归"现象的根源

### 修改6: 屏蔽加速度计修正（慢速更新）

**文件：** `applications/inav.c`
**位置：** 第361-379行
**函数：** `Pos_Vel_Upate()`

**原始代码：**
```c
if (alt_observation_health == 1)
{
    accel_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Z2], accel_correction_ef.z, dt, 0.2f);
    velocity_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Z], velocity_correction_ef.z, dt, 0.2f);
}
else
{
    inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef_lpf.z) * dt;
    inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef_lpf.z) * dt;
}
```

**修改后代码：**
```c
if (alt_observation_health == 1)
{
    // 200cm以下纯光流模式：屏蔽加速度计修正
    if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
        // 纯光流模式：仅使用观测修正，不使用加速度计修正
        velocity_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Z], velocity_correction_ef.z, dt, 0.2f);
        // 加速度修正设为0
        accel_correction_ef_lpf.z = 0.0f;
    } else {
        // 200cm以上：正常使用加速度计修正
        accel_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Z2], accel_correction_ef.z, dt, 0.2f);
        velocity_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Z], velocity_correction_ef.z, dt, 0.2f);
    }
}
else
{
    inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef_lpf.z) * dt;
    inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef_lpf.z) * dt;
}
```

**影响：**
- 200cm以下将加速度修正设为0
- 仅保留速度观测修正
- 进一步减少惯性导航的影响

## 📊 系统行为变化

### 高度测定数据源对比

**修改前（多传感器融合）：**
```
高度 = 陀螺仪+加速度计积分 + 气压计观测修正 + 光流观测修正（加权）
```

**修改后（200cm分段模式）：**
```
200cm以下: 高度 = 纯光流观测 + 光流速度观测修正
200cm以上: 高度 = 陀螺仪+加速度计积分 + 气压计观测修正
```

### alt_source标识变化

- `alt_source = 1`: 仅气压计（200cm以上）
- `alt_source = 2`: 光流+气压计融合（已移除）
- `alt_source = 3`: 纯光流测高（200cm以下，新增）

### 预期效果

**200cm以下的优势：**
- ✅ 消除"冲高后回归"现象
- ✅ 消除陀螺仪和加速度计的干扰
- ✅ 高度响应完全跟随光流传感器
- ✅ 消除多传感器融合的复杂性

**潜在风险：**
- ⚠️ 完全依赖光流传感器的可靠性
- ⚠️ 光流传感器故障时无备份
- ⚠️ 200cm切换点可能有跳变
- ⚠️ 失去惯性导航的平滑特性

## 🎯 测试建议

1. **静态测试**：地面静止时观察height值稳定性
2. **慢速升降测试**：观察200cm切换点的行为
3. **快速升降测试**：验证是否消除"冲高"现象
4. **光流遮挡测试**：验证200cm以上的气压计模式
5. **边界测试**：在200cm附近反复升降测试切换稳定性

## 📝 总结

通过6处关键修改，系统已完全改为200cm以下纯光流测高模式。所有修改都基于`flow.distance_cm <= 200.0f`条件，确保了逻辑一致性。这种修改彻底消除了多传感器融合在低空时的复杂性，但也增加了对光流传感器可靠性的依赖。
