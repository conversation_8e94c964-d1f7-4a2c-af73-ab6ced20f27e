#include "inav.h"
#include "ahrs.h"
#include "filter.h"
#include "drv_optflow.h"


void Pos_Vel_Upate(float dt);
inav_state_t inav_state;
inav_data_t inav_data;

#define    VERTICAL_BARO_FILTER_K                  0.01f

vector3f_t location_3d_diff_NEU(location_t loc1, location_t loc2);
vector3f_t  Acc_ef, Acc_ef_Lpf, Acc_bf, accel_correction_ef, velocity_correction_ef, position_correction_bf, accel_correction_ef_lpf, velocity_correction_ef_lpf;
vector3f_t  position_ef_M;
vector3f_t Accel_ef;
vector3f_t velocity_error_ef, position_error_ef;
location_t Home, Pos_Now, Inertialnav_Origin;
vector3f_t Home_Pos;
extern vector3f_t Bf_GPS_Offset_To_Ef;
float Ground_Speed;
uint8_t alt_observation_health = 1, vel_x_observation_health = 1, vel_y_observation_health = 1;
uint16_t GPS_Check_Delay = 0;
float gps_velocity_acc = 0;

vector3f_t vel_observation, pos_observation, raw_vel_observation;
void Horizontal_Observation_Health_Check(void)
{
    if ((gps_velocity_acc < 1.0f) && (inav_state.horizontal_observation_health == 0) && (inav_gps_detected == 1) && (inav_gps_numsv >= 5))
    {
        GPS_Check_Delay++;
        if (GPS_Check_Delay == 200)
        {
            inav_state.gps_health = 1;
            GPS_Check_Delay = 0;
        }
    }
    if ((gps_velocity_acc >= 1.3f) || (inav_gps_detected == 0) || (inav_gps_numsv < 6))
    {
        inav_state.gps_health = 0;
    }


    if (inav_flow_sqal >= 10)
    {
        inav_state.flow_health = 1;
    }
    else
    {
        inav_state.flow_health = 0;
    }

    if ((inav_state.gps_health == 1) || (inav_state.flow_health == 1))
    {
        inav_state.horizontal_observation_health = 1;
    }
    else
    {
        inav_state.horizontal_observation_health = 0;
    }
}


void Alt_Pos_reset()
{
    pos_observation.z = 0;
    inav_data.position_ef.z = 0;
    inav_data.velocity_ef.z = 0;
    position_error_ef.z = 0;
    velocity_error_ef.z = 0;
}


float Armed_Yaw = 0;
float Baro_Alt_Aemed_Offset;
uint8_t Alt_Reset_State = 0;;
void Get_Home(void)
{
    if (Copter.ARMED == 0)
    {
        inav_state.home_state = 0;
        Alt_Reset_State = 0;
    }

    if ((inav_state.home_state == 0) && (inav_state.gps_health == 1) && (Copter.ARMED == 1))
    {
        Home.lat = inav_gps_latitude;
        Home.lng = inav_gps_longitude;
        Home.alt = 0;
        Home_Pos.x = inav_data.position_ef.x;
        Home_Pos.y = inav_data.position_ef.y;
        Home_Pos.z = inav_data.position_ef.z;
        inav_state.home_state = 1;
    }

    if (inav_state.home_state == 0)
    {
        Home.lat = 228910329;
        Home.lng = **********;
        Home.alt = 10;
        Home_Pos.x = 0;
        Home_Pos.y = 0;
        Home_Pos.z = 0;
    }
    if ((Alt_Reset_State == 0) && (Copter.ARMED == 1))
    {
        Armed_Yaw = inav_data.euler_angle.yaw;
        Alt_Pos_reset();
        Baro_Alt_Aemed_Offset = sensors.baro.alt_cm_lpf;
        Alt_Reset_State = 1;
    }
}

void Get_Inertialnav_Origin(void)
{
    if ((inav_state.inertialnav_origin_state == 0) && (inav_state.gps_health == 1))
    {
        Inertialnav_Origin.lat = inav_gps_latitude;
        Inertialnav_Origin.lng = inav_gps_longitude;
        Inertialnav_Origin.alt = 0;
        inav_state.inertialnav_origin_state = 1;
    }
}

vector3f_t delayed_velocity_ef, delayed_acc_ef;
uint8_t alt_source = 0, vel_x_source = 0, vel_y_source = 0;
uint16_t alt_observation_unhealthy_count = 0, vel_x_observation_unhealthy_count, vel_y_observation_unhealthy_count, max_vel_z_error = 2000, max_vel_xy_error = 500;
vector3f_t position_ef_M_From_Loc;
float alt_source_lpf = 0;
//对速度位置原始观测数据做切换和异常处理
void Get_Pos_Vel_Observation(float dt)
{
    //z - 200cm分段模式：直接光流 vs 加权融合
    // 使用光流距离判断高度范围
    if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
        // 200cm以下：直接使用光流速度，无任何运算
        raw_vel_observation.z = flow.altitude_velocity;
        alt_source = 3; // 3表示直接光流模式
    } else if ((flow.distance_health == 1) && (flow.altitude_weight > 0.01f)) {
        // 200cm以上：恢复光流+气压计加权融合
        raw_vel_observation.z = inav_baro_vel * (1.0f - flow.altitude_weight) + flow.altitude_velocity * flow.altitude_weight;
        alt_source = 2; // 2表示光流+气压计融合
    } else {
        // 光流不健康：仅使用气压计
        raw_vel_observation.z = inav_baro_vel;
        alt_source = 1; // 1表示仅气压计
    }


    alt_source_lpf = Lowpass_Filter(&LPF[LPF_ALT_SOURCE], raw_vel_observation.z, dt, 0.2);

    if (fabs(raw_vel_observation.z - delayed_velocity_ef.z) > max_vel_z_error)
    {
        alt_observation_unhealthy_count++;
        alt_observation_health = 0;
    }
    else
    {
        alt_observation_unhealthy_count = 0;
        alt_observation_health = 1;
    }
    if (alt_observation_unhealthy_count >= 2000) //无观测纯惯导计算时间
    {
        alt_observation_health = 1;
    }

    if (alt_observation_health == 1)
    {
        vel_observation.z = raw_vel_observation.z;
    }
    else
    {
        vel_observation.z = delayed_velocity_ef.z;
    }

    // 位置观测融合 - 200cm分段模式：直接光流 vs 加权融合
    float baroAltTemp = inav_baro_alt;// - Baro_Alt_Aemed_Offset;
    if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
        // 200cm以下：直接使用光流距离，完全同步传值
        pos_observation.z = flow.distance_cm;// - Baro_Alt_Aemed_Offset; // 直接赋值，无任何滤波运算
    } else if ((flow.distance_health == 1) && (flow.altitude_weight > 0.01f)) {
        // 200cm以上：恢复光流+气压计加权融合
        float flow_alt_temp = flow.distance_cm - Baro_Alt_Aemed_Offset;
        float fused_alt_temp = baroAltTemp * (1.0f - flow.altitude_weight) + flow_alt_temp * flow.altitude_weight;
        pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (fused_alt_temp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
    } else {
        // 光流不健康：仅使用气压计位置观测
        pos_observation.z += vel_observation.z * dt * (1 - VERTICAL_BARO_FILTER_K) + (baroAltTemp - pos_observation.z) * VERTICAL_BARO_FILTER_K;
    }

    if (inav_state.horizontal_observation_health == 0)
    {
        pos_observation.x = position_ef_M_From_Loc.x * 100;
        pos_observation.y = position_ef_M_From_Loc.y * 100;
        inav_data.position_ef.x = pos_observation.x;
        inav_data.velocity_ef.x = 0;
        position_error_ef.x = 0;
        velocity_error_ef.x = 0;
        inav_data.position_ef.y = pos_observation.y;
        inav_data.velocity_ef.y = 0;
        position_error_ef.y = 0;
        velocity_error_ef.y = 0;
    }

    if (inav_state.flow_health == 1)
    {
        raw_vel_observation.x = inav_flow_vel_x;
        vel_x_source = 1;
    }
    if (inav_state.gps_health == 1)
    {
        raw_vel_observation.x = inav_gps_vel_n;
        vel_x_source = 0;
    }
    if (fabs(raw_vel_observation.x - delayed_velocity_ef.x) > max_vel_xy_error)
    {
        vel_x_observation_unhealthy_count++;
        vel_x_observation_health = 0;
    }
    else
    {
        vel_x_observation_unhealthy_count = 0;
        vel_x_observation_health = 1;
    }
    if (vel_x_observation_unhealthy_count >= 2000) //无观测纯惯导计算时间
    {
        vel_x_observation_health = 1;
    }
    if (vel_x_observation_health == 1)
    {
        vel_observation.x = raw_vel_observation.x; //constrain_float(raw_vel_observation.x,delayed_velocity_ef.x-max_vel_xy_error,delayed_velocity_ef.x+max_vel_xy_error);
    }
    else
    {
        vel_observation.x = delayed_velocity_ef.x;
    }
    pos_observation.x += vel_observation.x * dt;

    //y
    if (inav_state.flow_health == 1)
    {
        raw_vel_observation.y = inav_flow_vel_y;
        vel_y_source = 1;
    }
    if (inav_state.gps_health == 1)
    {
        raw_vel_observation.y = inav_gps_vel_e;
        vel_y_source = 0;
    }
    if (fabs(raw_vel_observation.y - delayed_velocity_ef.y) > max_vel_xy_error)
    {
        vel_y_observation_unhealthy_count++;
        vel_y_observation_health = 0;
    }
    else
    {
        vel_y_observation_unhealthy_count = 0;
        vel_y_observation_health = 1;
    }
    if (vel_y_observation_unhealthy_count >= 2000) //无观测纯惯导计算时间
    {
        vel_y_observation_health = 1;
    }

    if (vel_y_observation_health == 1)
    {
        vel_observation.y = raw_vel_observation.y; //constrain_float(raw_vel_observation.y,delayed_velocity_ef.y-max_vel_xy_error,delayed_velocity_ef.y+max_vel_xy_error);
    }
    else
    {
        vel_observation.y = delayed_velocity_ef.y;
    }
    pos_observation.y += vel_observation.y * dt;
}

float vel_xy_kp = 0.7f, vel_z_kp = 1.2f;
float pos_xy_kp = 0.5f, pos_z_kp = 0.8f;
float Yaw_Error;
float Distance_Home_To_Loc_Now;
float Yaw_Error_Last = 0, Yaw_Error_Lpf = 0;
extern float Targe_Pos_Ef_x, Targe_Pos_Ef_y;
float GPS_ACC_N, GPS_ACC_E, GPS_Vel_E_Lpf, GPS_Vel_N_Lpf, Delayed_ACC_Ef_x, Delayed_ACC_Ef_y;
vector3f_t GPS_Offset;
euler_angle_t inav_angle_error_from_gps;
uint32_t Bearing_Home_To_Pos_Now = 0, Distance_Home_To_Pos_Now;
float YAW_IMU, YAW_GPS;
vector3f_t acc_ef_bias;
uint16_t get_acc_bais_time_delay = 0;
float flow_scale_raw = 0.5f, flow_scale_correction_x, flow_scale_correction_y;
void Pos_Vel_Upate(float dt)
{
    static uint8_t i = 0;

    gps_velocity_acc = Lowpass_Filter(&LPF[LPF_GPS_VEL_AC], inav_gps_vel_acc, dt, 0.5f);
    Acc_ef_Lpf.x = Lowpass_Filter(&LPF[LPF_ACC_EF_X], Acc_ef.x, dt, 4.0f);
    Acc_ef_Lpf.y = Lowpass_Filter(&LPF[LPF_ACC_EF_Y], Acc_ef.y, dt, 4.0f);
    Acc_ef_Lpf.z = Lowpass_Filter(&LPF[LPF_ACC_EF_Z], Acc_ef.z, dt, 4.0f);

    Horizontal_Observation_Health_Check();
    Get_Home();
    Get_Inertialnav_Origin();

    Pos_Now.lat = inav_gps_latitude;
    Pos_Now.lng = inav_gps_longitude;
    Pos_Now.alt = inav_gps_height;

    if (inav_state.inertialnav_origin_state == 1)
    {
        position_ef_M_From_Loc = location_3d_diff_NEU(Inertialnav_Origin, Pos_Now);
    }

    Get_Pos_Vel_Observation(dt);

    position_error_ef.x = pos_observation.x - Data_Delay(&DELAY[DELAY_POS_EF_X], inav_data.position_ef.x, 25);
    position_error_ef.y = pos_observation.y - Data_Delay(&DELAY[DELAY_POS_EF_Y], inav_data.position_ef.y, 25);
    position_error_ef.z = pos_observation.z - Data_Delay(&DELAY[DELAY_POS_EF_Z], inav_data.position_ef.z, 30);

    delayed_velocity_ef.x = Data_Delay(&DELAY[DELAY_VEL_EF_X], inav_data.velocity_ef.x, 25);
    delayed_velocity_ef.y = Data_Delay(&DELAY[DELAY_VEL_EF_Y], inav_data.velocity_ef.y, 25);
    delayed_velocity_ef.z = Data_Delay(&DELAY[DELAY_VEL_EF_Z], inav_data.velocity_ef.z, 30);

    velocity_error_ef.x = vel_observation.x - delayed_velocity_ef.x;
    velocity_error_ef.y = vel_observation.y - delayed_velocity_ef.y;
    velocity_error_ef.z = vel_observation.z - delayed_velocity_ef.z;
    velocity_error_ef.z = constrain_float(velocity_error_ef.z, -(fabs(alt_source_lpf - delayed_velocity_ef.z) + 20), (fabs(alt_source_lpf - delayed_velocity_ef.z) + 20));



    velocity_correction_ef.x = position_error_ef.x * pos_xy_kp;
    accel_correction_ef.x = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_X1], velocity_error_ef.x * vel_xy_kp, dt, 0.5f);
    if (vel_x_observation_health == 1)
    {
        accel_correction_ef_lpf.x = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_X2], accel_correction_ef.x, dt, 0.02f);
        velocity_correction_ef_lpf.x = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_X], velocity_correction_ef.x, dt, 0.02f);
//      inav_out.velocity_ef.x+=(Acc_ef.x+accel_correction_ef.x)*dt;
//      inav_out.position_ef.x+=(inav_out.velocity_ef.x+velocity_correction_ef.x)*dt;
    }
    else
    {
        inav_data.velocity_ef.x += (Acc_ef.x + accel_correction_ef_lpf.x) * dt;
        inav_data.position_ef.x += (inav_data.velocity_ef.x + velocity_correction_ef_lpf.x) * dt;
    }


    velocity_correction_ef.y = position_error_ef.y * pos_xy_kp;
    accel_correction_ef.y = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Y1], velocity_error_ef.y * vel_xy_kp, dt, 0.5f);
    if (vel_y_observation_health == 1)
    {
        accel_correction_ef_lpf.y = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Y2], accel_correction_ef.y, dt, 0.02f);
        velocity_correction_ef_lpf.y = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Y], velocity_correction_ef.y, dt, 0.02f);
//      inav_out.velocity_ef.y+=(Acc_ef.y+accel_correction_ef.y)*dt;
//      inav_out.position_ef.y+=(inav_out.velocity_ef.y+velocity_correction_ef.y)*dt;
    }
    else
    {
        inav_data.velocity_ef.y += (Acc_ef.y + accel_correction_ef_lpf.y) * dt;
        inav_data.position_ef.y += (inav_data.velocity_ef.y + velocity_correction_ef_lpf.y) * dt;
    }


    velocity_correction_ef.z = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Z1], position_error_ef.z * pos_z_kp, dt, 0.4f);
    accel_correction_ef.z = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Z1], velocity_error_ef.z * vel_z_kp, dt, 0.6f);
    if (get_acc_bais_time_delay < 7000)
    {
        get_acc_bais_time_delay++;
        acc_ef_bias.z += (accel_correction_ef.z) * dt * 0.4f;
        acc_ef_bias.z = constrain_float(acc_ef_bias.z, -15, 15);
    }
    if (alt_observation_health == 1)
    {
        // 200cm分段模式：直接光流 vs 正常修正
        if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
            // 200cm以下：直接光流模式，最小化修正运算
            velocity_correction_ef_lpf.z = 0.0f; // 不使用速度修正
            accel_correction_ef_lpf.z = 0.0f;    // 不使用加速度修正
        } else {
            // 200cm以上：恢复正常的加速度计修正
            accel_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_ACC_CORRECTION_EF_Z2], accel_correction_ef.z, dt, 0.2f);
            velocity_correction_ef_lpf.z = Lowpass_Filter(&LPF[LPF_VEL_CORRECTION_EF_Z], velocity_correction_ef.z, dt, 0.2f);
        }
    }
    else
    {
        inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef_lpf.z) * dt;
        inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef_lpf.z) * dt;
    }


    //航向旋转
    inav_data.velocity_bf.x = inav_data.velocity_ef.x * arm_cos_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw)) + inav_data.velocity_ef.y * arm_sin_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw));
    inav_data.velocity_bf.y = -inav_data.velocity_ef.x * arm_sin_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw)) + inav_data.velocity_ef.y * arm_cos_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw));

    delayed_acc_ef.x = Data_Delay(&DELAY[DELAY_ACC_EF_X], Acc_ef_Lpf.x, 120);
    delayed_acc_ef.y = Data_Delay(&DELAY[DELAY_ACC_EF_Y], Acc_ef_Lpf.y, 120);

    if (inav_state.horizontal_observation_health == 1)
    {
        GPS_Vel_E_Lpf = Lowpass_Filter(&LPF[LPF_GPS_VEL_E], raw_vel_observation.y, dt, 2.0f);
        GPS_Vel_N_Lpf = Lowpass_Filter(&LPF[LPF_GPS_VEL_N], raw_vel_observation.x, dt, 2.0f);
    }

    i++;
    if (i == 25)
    {
        if (inav_state.gps_health == 1)
        {
            Distance_Home_To_Loc_Now = get_distance_cm(Home, Pos_Now);
            Distance_Home_To_Pos_Now = sqrtf((Targe_Pos_Ef_x - Home_Pos.x) * (Targe_Pos_Ef_x - Home_Pos.x) + (Targe_Pos_Ef_y - Home_Pos.y) * (Targe_Pos_Ef_y - Home_Pos.y));
            Bearing_Home_To_Pos_Now = get_bearing_cd(Home, Pos_Now) / 100;
        }

        GPS_ACC_E = Get_Derivator(&DERIVATOR[DERIVATOR_GPS_VEl_E_LPF], GPS_Vel_E_Lpf, 25 * dt);
        GPS_ACC_N = Get_Derivator(&DERIVATOR[DERIVATOR_GPS_VEl_N_LPF], GPS_Vel_N_Lpf, 25 * dt);

        inav_data.gps_acc_ef.x = GPS_ACC_N;
        inav_data.gps_acc_ef.y = GPS_ACC_E;

        YAW_IMU = MATH_RAD_TO_DEG(atan2f(delayed_acc_ef.y, delayed_acc_ef.x));
        YAW_GPS = MATH_RAD_TO_DEG(atan2f(GPS_ACC_E, GPS_ACC_N));
        Yaw_Error = -Get_Angle_Error(YAW_GPS, YAW_IMU);
        inav_angle_error_from_gps.yaw = Yaw_Error;

        vector3f_t accel_error;
        accel_error.x = GPS_ACC_N - delayed_acc_ef.x;
        accel_error.y = GPS_ACC_E - delayed_acc_ef.y;

        inav_angle_error_from_gps.pitch = accel_error.x * arm_cos_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw)) + accel_error.y * arm_sin_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw));
        inav_angle_error_from_gps.roll = -accel_error.x * arm_sin_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw)) + accel_error.y * arm_cos_f32(MATH_DEG_TO_RAD(inav_data.euler_angle.yaw));

        inav_angle_error_from_gps.roll = atan2f(inav_angle_error_from_gps.roll, 980);
        inav_angle_error_from_gps.pitch = atan2f(inav_angle_error_from_gps.pitch, 980) * sign(bf_z_to_ef_z);


        if ((delayed_acc_ef.x > 0) && (GPS_ACC_N > 0))
        {
            flow_scale_correction_x = delayed_acc_ef.x - GPS_ACC_N;
        }
        if ((delayed_acc_ef.x < 0) && (GPS_ACC_N < 0))
        {
            flow_scale_correction_x = GPS_ACC_N - delayed_acc_ef.x;
        }

        if ((delayed_acc_ef.y > 0) && (GPS_ACC_E > 0))
        {
            flow_scale_correction_y = delayed_acc_ef.y - GPS_ACC_E;
        }
        if ((delayed_acc_ef.y < 0) && (GPS_ACC_E < 0))
        {
            flow_scale_correction_y = GPS_ACC_E - delayed_acc_ef.y;
        }

        flow_scale_raw += (flow_scale_correction_x + flow_scale_correction_y) * 25 * dt * 0.001;
        i = 0;
    }
    inav_data.flow_scale = 1; //Lowpass_Filter(&LPF[LPF_FLOW_SCALE],flow_scale_raw,dt,1.0f);
    Ground_Speed = Lowpass_Filter(&LPF[LPF_GROUND_SPEED], inav_gps_ground_speed, dt, 1.5f);
}



float longitude_scale(location_t loc)
{
    float scale = arm_cos_f32(MATH_DEG_TO_RAD(loc.lat * 1.0e-7f));
    return constrain_float(scale, 0.01f, 1.0f);
}

float get_distance(location_t loc1, location_t loc2)
{
    float dlat              = (float)(loc2.lat - loc1.lat);
    float dlong             = ((float)(loc2.lng - loc1.lng)) * longitude_scale(loc2);
    return (sqrtf(dlat * dlat + dlong * dlong)) * LOCATION_SCALING_FACTOR;
}

uint32_t get_distance_cm(location_t loc1, location_t loc2)
{
    return get_distance(loc1, loc2) * 100;
}


float get_bearing_cd(location_t loc1, location_t loc2)
{
    int32_t off_x = loc2.lng - loc1.lng;
    int32_t off_y = (loc2.lat - loc1.lat) / longitude_scale(loc2);
    float bearing = 9000 + atan2f(-off_y, off_x) * 5729.57795f;
    if (bearing < 0) bearing += 36000;
    return bearing;
}

vector3f_t location_3d_diff_NEU(location_t loc1, location_t loc2)
{
    vector3f_t Pos_Measure;
    Pos_Measure.x = (loc2.lat - loc1.lat) * LOCATION_SCALING_FACTOR;
    Pos_Measure.y = (loc2.lng - loc1.lng) * LOCATION_SCALING_FACTOR * longitude_scale(loc1);
    Pos_Measure.z = (loc1.alt - loc2.alt) * 0.01f;
    return Pos_Measure;
}


void inav_vel_pos_fast_update(float dt)
{
    inav_data.acc_ef.x = (sensors.acc_cm.x) * (q0q0 + q1q1 - q2q2 - q3q3)  + 2 * (sensors.acc_cm.y) * (q1q2 - q0q3)                       + 2 * (sensors.acc_cm.z) * (q1q3 + q0q2);
    inav_data.acc_ef.y = 2 * (sensors.acc_cm.x) * (q1q2 + q0q3)                  + (sensors.acc_cm.y) * (q0q0 - q1q1 + q2q2 - q3q3)       + 2 * (sensors.acc_cm.z) * (q2q3 - q0q1);
    inav_data.acc_ef.z = (sensors.acc_cm.x * 2 * (q1q3 - q0q2)                + sensors.acc_cm.y * 2 * (q0q1 + q2q3)                       + (sensors.acc_cm.z) * (q0q0 - q1q1 - q2q2 + q3q3)) - 980 * (q0q0 - q1q1 - q2q2 + q3q3);

    Acc_ef.x = (inav_data.acc_ef.x);
    Acc_ef.y = (-inav_data.acc_ef.y);
    Acc_ef.z = (inav_data.acc_ef.z) + acc_ef_bias.z; //-acc_ef_bias.z;

		// 添加姿态限制保护
		// ========== 新增代码开始 ==========
		Acc_ef.z = (inav_data.acc_ef.z) + acc_ef_bias.z; //-acc_ef_bias.z;

		// 姿态限制保护 - 防止大倾斜角时高度估计异常
		float tilt_compensation = fabs(q0q0 - q1q1 - q2q2 + q3q3);
		if (tilt_compensation < 0.7f) {  // 倾斜角度超过45°时 (cos(45°) ≈ 0.707)
				// 降低加速度计权重，强制使用气压计观测
				alt_observation_health = 1;  // 强制启用高度观测修正
				
				// 增加修正增益以快速收敛
				float tilt_factor = 0.7f / tilt_compensation;  // 倾斜越大，修正越强
				tilt_factor = constrain_float(tilt_factor, 1.0f, 3.0f);  // 限制在1-3倍之间
				
				// 临时增加修正增益
				velocity_correction_ef.z *= tilt_factor;
				accel_correction_ef.z *= tilt_factor;
		}
// ========== 新增代码结束 ==========


    if (inav_state.ahrs_alignment_complete == 1)
    {
        if (alt_observation_health == 1)
        {
            // 200cm分段模式：直接光流 vs 惯导积分
            if ((flow.distance_health == 1) && (flow.distance_cm <= 200.0f)) {
                // 200cm以下：直接使用光流观测，不进行惯导积分
                // inav_data.velocity_ef.z 和 inav_data.position_ef.z 直接由观测更新
            } else {
                // 200cm以上：正常使用陀螺仪+加速度计惯导积分
                inav_data.velocity_ef.z += (Acc_ef.z + accel_correction_ef.z) * dt;
                inav_data.position_ef.z += (inav_data.velocity_ef.z + velocity_correction_ef.z) * dt;
            }
        }

        if (vel_x_observation_health == 1)
        {
            inav_data.velocity_ef.x += (Acc_ef.x + accel_correction_ef.x) * dt;
            inav_data.position_ef.x += (inav_data.velocity_ef.x + velocity_correction_ef.x) * dt;
        }

        if (vel_y_observation_health == 1)
        {
            inav_data.velocity_ef.y += (Acc_ef.y + accel_correction_ef.y) * dt;
            inav_data.position_ef.y += (inav_data.velocity_ef.y + velocity_correction_ef.y) * dt;
        }
    }
}



