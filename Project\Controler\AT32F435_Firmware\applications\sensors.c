#include "parameters.h"
#include "sensors.h"
#include "sensor_calibrate.h"
#include "inav.h"
#include "filter.h"
#include "drv_bmi088.h"
#include "drv_spl06xx.h"
#include "drv_optflow.h"

sensors_t  sensors;

lowpass_filter_t LPF[LPF_NUM];
derivator_t DERIVATOR[DERIVATOR_NUM];
data_delay_t DELAY[DELAY_NUM];

// 气压计重新校准控制变量
uint8_t baro_recalibrate_flag = 0;

static lowpass_filter2p_t baro_2pf;

static lowpass_filter2p_t gyro_2pf[3];

static lowpass_filter2p_t accel_2pf[3];

float gyro_lpf_cutoff_frequency = 100;

uint16_t GYRO_CALIBRATE_DELLAY = 0;

#define CONST_PF 0.1902630958 //(1/5.25588f) Pressure factor
#define P0 101325.0f          // standard static pressure at sea level
/**
 * Converts pressure to altitude above sea level (ASL) in meters
 */
static float PressureToAltitude(baro_data_t *data)
{
    if (data->pressure > 0)
    {
        return ((pow((P0 / data->pressure), CONST_PF) - 1.0f) * (data->temperature + 273.15f)) / 0.0065f * 100; //in cm
    }
    else
    {
        return 0;
    }
}

void sensors_init(void)
{
    bmi088_hw_init("spi20","spi21");
    spl0601_hw_init("spi22");
}


void sensors_gyro_update(void)
{
    bmi088_read_gyro(&sensors.gyro_raw);
 
    if (Copter.ACC_CALIBRATE || Copter.GYRO_CALIBRATE || Copter.MAG_CALIBRATE)
    {
        gyro_lpf_cutoff_frequency = 10.0f;
        if (GYRO_CALIBRATE_DELLAY < 1000)
        {
            GYRO_CALIBRATE_DELLAY++;
        }
    }
    else
    {
        gyro_lpf_cutoff_frequency = 60.0f;
        GYRO_CALIBRATE_DELLAY = 0;
    }
    
    sensors.gyro_lpf.x = Lowpass_Filter(&LPF[LPF_GYRO_X], (float)sensors.gyro_raw.x, 0.001f, gyro_lpf_cutoff_frequency);
    sensors.gyro_lpf.y = Lowpass_Filter(&LPF[LPF_GYRO_Y], (float)sensors.gyro_raw.y, 0.001f, gyro_lpf_cutoff_frequency);
    sensors.gyro_lpf.z = Lowpass_Filter(&LPF[LPF_GYRO_Z], (float)sensors.gyro_raw.z, 0.001f, 30);

    sensors.gyro.x = (sensors.gyro_lpf.x - parameters.data.gyro_offset.x);
    sensors.gyro.y = (sensors.gyro_lpf.y - parameters.data.gyro_offset.y);
    sensors.gyro.z = (sensors.gyro_lpf.z - parameters.data.gyro_offset.z);

    sensors.gyro_dps.x = sensors.gyro.x / 32.768f;
    sensors.gyro_dps.y = sensors.gyro.y / 32.768f;
    sensors.gyro_dps.z = sensors.gyro.z / 32.768f;

    //��Ϊ������
    sensors.gyro_rps.x = MATH_DEG_TO_RAD(sensors.gyro_dps.x);
    sensors.gyro_rps.y = MATH_DEG_TO_RAD(sensors.gyro_dps.y);
    sensors.gyro_rps.z = MATH_DEG_TO_RAD(sensors.gyro_dps.z);


    if (GYRO_CALIBRATE_DELLAY == 1000)
    {
        Gyro_Calibrate();
    }
}

void sensors_accel_update(void)
{
    bmi088_read_accel(&sensors.accel_raw);

    sensors.accel_lpf.x = Lowpass_Filter(&LPF[LPF_ACC_X], sensors.accel_raw.x, 0.001f, 10.0f);
    sensors.accel_lpf.y = Lowpass_Filter(&LPF[LPF_ACC_Y], sensors.accel_raw.y, 0.001f, 10.0f);
    sensors.accel_lpf.z = Lowpass_Filter(&LPF[LPF_ACC_Z], sensors.accel_raw.z, 0.001f, 10.0f);

    sensors.accel.x = (sensors.accel_lpf.x - parameters.data.accel_offset.x) * parameters.data.accel_scale.x;
    sensors.accel.y = (sensors.accel_lpf.y - parameters.data.accel_offset.y) * parameters.data.accel_scale.y;
    sensors.accel.z = (sensors.accel_lpf.z - parameters.data.accel_offset.z) * parameters.data.accel_scale.z;

    sensors.acc_cm.x = sensors.accel.x / ACC_1G * 980.0f;
    sensors.acc_cm.y = sensors.accel.y / ACC_1G * 980.0f;
    sensors.acc_cm.z = sensors.accel.z / ACC_1G * 980.0f;
}

void sensors_mag_update(void)
{
//    st480mc_read_mag(&sensors.mag_raw);

//    sensors.mag.x = (sensors.mag_raw.x - parameters.data.mag_offset.x) * parameters.data.mag_scale.x;
//    sensors.mag.y = (sensors.mag_raw.y - parameters.data.mag_offset.y) * parameters.data.mag_scale.y;
//    sensors.mag.z = (sensors.mag_raw.z - parameters.data.mag_offset.z) * parameters.data.mag_scale.z;

}
void sensors_baro_update(void)
{
    static uint8_t i = 0,count = 0;
    static uint8_t offset_state = 0;
    static float sum = 0;

    // 零点重新校准相关变量
    static uint8_t recalib_request = 0;
    static uint8_t recalib_count = 0;
    static float recalib_sum = 0;
    
    if (i <= 150)
        i++;
    spl06_read(&sensors.baro);

    sensors.baro.pressure_alt_cm = PressureToAltitude(&sensors.baro);

    if ((i > 20) && (i <= 70))
    {
        sum += sensors.baro.pressure_alt_cm;
    }
    if (i > 70)
    {
        sensors.baro.pressure_alt_cm_offset = sum / 50;
        offset_state = 1;
    }

    // 检查是否需要重新校准零点
    extern uint8_t baro_recalibrate_flag; // 外部重新校准请求标志
    if (baro_recalibrate_flag && offset_state == 1) {
        recalib_request = 1;
        recalib_count = 0;
        recalib_sum = 0;
        baro_recalibrate_flag = 0; // 清除请求标志
    }

    // 执行重新校准过程
    if (recalib_request) {
        if (recalib_count < 50) {
            recalib_sum += sensors.baro.pressure_alt_cm;
            recalib_count++;
        } else {
            // 完成重新校准
            sensors.baro.pressure_alt_cm_offset = recalib_sum / 50;
            recalib_request = 0;
            recalib_count = 0;
            recalib_sum = 0;
        }
    }

    if (offset_state == 1)
    {
        if (inav_state.gps_health == 1)
        {
            sensors.baro.alt_cm = (sensors.baro.pressure_alt_cm - sensors.baro.pressure_alt_cm_offset) - inav_gps_ground_speed * 0.3f;
        }
        else
        {
            sensors.baro.alt_cm = sensors.baro.pressure_alt_cm - sensors.baro.pressure_alt_cm_offset;
        }

        sensors.baro.alt_cm_lpf = Lowpass_Filter2p(&baro_2pf, sensors.baro.alt_cm,0.02f, 10); //;Lowpass_Filter(&LPF[LPF_SPL06_ALT],spl06_alt_cm,0.02,10);
        count++;
       if (count >=5)
        {
        count = 0;
        sensors.baro.vel_cm = Get_Derivator(&DERIVATOR[DERIVATOR_SPL06_ALT], sensors.baro.alt_cm_lpf, 0.1);
        }
    }
}

// 光流测高更新函数，参照sensors_baro_update()模式
void sensors_flow_altitude_update(void)
{
    // 调用光流测高处理函数，dt=0.02f对应50Hz更新频率
    Process_Flow_Data(0.02f);

    // 200cm以下纯光流测高模式权重计算
    if (flow.distance_health == 1 && flow.distance_cm <= 200.0f) {
        flow.altitude_weight = 1.0f; // 200cm以下：纯光流测高
    } else {
        flow.altitude_weight = 0.0f; // 200cm以上或光流不健康：禁用光流测高
    }

    // 设置光流测高融合激活标志 - 200cm以下纯光流模式
    flow.altitude_fusion_active = (flow.distance_health == 1 && flow.distance_cm <= 200.0f) ? 1 : 0;
}

// 气压计零点重新校准函数
void sensors_baro_recalibrate(void)
{
    baro_recalibrate_flag = 1; // 设置重新校准请求标志
}
